#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的API调用统计测试
专注于验证API调用统计的核心功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.record_config import SyncRecord


def test_sync_record_api_counting():
    """测试SyncRecord的API调用统计功能"""
    print("=" * 60)
    print("测试SyncRecord API调用统计功能")
    print("=" * 60)
    
    # 创建同步记录
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    
    print(f"初始API调用次数: {sync_record.api_call_count}")
    
    # 模拟API调用统计
    expected_calls = 25
    for i in range(expected_calls):
        sync_record.api_call_count += 1
        sync_record.add_performance_metric(f"api_call_{i}_duration", 0.1 + i * 0.01)
    
    print(f"模拟{expected_calls}次API调用后: {sync_record.api_call_count}")
    print(f"性能指标数量: {len(sync_record.performance_metrics)}")
    
    # 完成同步记录
    sync_record.complete('success')
    
    # 转换为字典
    record_dict = sync_record.to_dict()
    
    print(f"字典中的API调用次数: {record_dict['performance']['api_call_count']}")
    print(f"字典中的性能指标数量: {len(record_dict['performance']['metrics'])}")
    
    # 验证
    api_count_correct = record_dict['performance']['api_call_count'] == expected_calls
    metrics_count_correct = len(record_dict['performance']['metrics']) == expected_calls
    
    print(f"\n验证结果:")
    print(f"  - API调用次数正确: {api_count_correct} ({record_dict['performance']['api_call_count']} == {expected_calls})")
    print(f"  - 性能指标数量正确: {metrics_count_correct} ({len(record_dict['performance']['metrics'])} == {expected_calls})")
    
    if api_count_correct and metrics_count_correct:
        print("✅ SyncRecord API调用统计测试通过!")
        return True
    else:
        print("❌ SyncRecord API调用统计测试失败!")
        return False


def test_api_call_timing_simulation():
    """模拟API调用时机问题的修复前后对比"""
    print("\n" + "=" * 60)
    print("模拟API调用时机问题修复前后对比")
    print("=" * 60)
    
    print("修复前的问题场景:")
    print("1. 数据获取阶段：_current_sync_record = None")
    print("2. API调用不被统计")
    print("3. 同步阶段：创建sync_record")
    print("4. 只有同步阶段的API调用被统计")
    
    # 模拟修复前
    current_sync_record_before = None
    data_fetch_calls = 40  # 数据获取阶段的API调用
    sync_calls = 5  # 同步阶段的API调用
    
    # 数据获取阶段（修复前：不统计）
    recorded_fetch_calls_before = 0
    for i in range(data_fetch_calls):
        if current_sync_record_before:  # 这里为None，所以不统计
            recorded_fetch_calls_before += 1
    
    # 同步阶段（修复前：统计）
    sync_record_before = SyncRecord("TEST", 'full', 'cloud_to_internal')
    current_sync_record_before = sync_record_before
    for i in range(sync_calls):
        if current_sync_record_before:
            current_sync_record_before.api_call_count += 1
    
    total_before = recorded_fetch_calls_before + sync_record_before.api_call_count
    expected_total = data_fetch_calls + sync_calls
    
    print(f"\n修复前结果:")
    print(f"  - 数据获取阶段API调用: {data_fetch_calls}次，统计: {recorded_fetch_calls_before}次")
    print(f"  - 同步阶段API调用: {sync_calls}次，统计: {sync_record_before.api_call_count}次")
    print(f"  - 总计: 实际{expected_total}次，统计{total_before}次")
    print(f"  - 丢失: {expected_total - total_before}次")
    
    print("\n修复后的改进:")
    print("1. 提前创建sync_record")
    print("2. 数据获取阶段：_current_sync_record 有效")
    print("3. 所有API调用都被统计")
    
    # 模拟修复后
    sync_record_after = SyncRecord("TEST", 'full', 'cloud_to_internal')
    current_sync_record_after = sync_record_after
    
    # 数据获取阶段（修复后：统计）
    for i in range(data_fetch_calls):
        if current_sync_record_after:
            current_sync_record_after.api_call_count += 1
    
    # 同步阶段（修复后：继续统计）
    for i in range(sync_calls):
        if current_sync_record_after:
            current_sync_record_after.api_call_count += 1
    
    total_after = sync_record_after.api_call_count
    
    print(f"\n修复后结果:")
    print(f"  - 数据获取阶段API调用: {data_fetch_calls}次，统计: {data_fetch_calls}次")
    print(f"  - 同步阶段API调用: {sync_calls}次，统计: {sync_calls}次")
    print(f"  - 总计: 实际{expected_total}次，统计{total_after}次")
    print(f"  - 改进: +{total_after - total_before}次")
    
    # 验证
    fix_successful = total_after == expected_total and total_before < expected_total
    
    print(f"\n验证结果:")
    print(f"  - 修复前统计不完整: {total_before < expected_total}")
    print(f"  - 修复后统计完整: {total_after == expected_total}")
    print(f"  - 修复成功: {fix_successful}")
    
    if fix_successful:
        print("✅ API调用时机修复验证通过!")
        return True
    else:
        print("❌ API调用时机修复验证失败!")
        return False


def test_performance_dict_structure():
    """测试性能统计字典结构的完整性"""
    print("\n" + "=" * 60)
    print("测试性能统计字典结构完整性")
    print("=" * 60)
    
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    
    # 设置各种性能数据
    sync_record.api_call_count = 15
    sync_record.fetch_duration = 2.5
    sync_record.transform_duration = 1.2
    sync_record.update_duration = 3.1
    sync_record.add_performance_metric("custom_metric_1", 0.8)
    sync_record.add_performance_metric("custom_metric_2", 1.5)
    
    # 完成同步
    sync_record.complete('success')
    
    # 转换为字典
    record_dict = sync_record.to_dict()
    performance = record_dict['performance']
    
    print("性能统计结构:")
    print(f"  - total_duration: {performance['total_duration']}")
    print(f"  - fetch_duration: {performance['fetch_duration']}")
    print(f"  - transform_duration: {performance['transform_duration']}")
    print(f"  - update_duration: {performance['update_duration']}")
    print(f"  - api_call_count: {performance['api_call_count']}")
    print(f"  - metrics: {len(performance['metrics'])} 项")
    
    # 验证必需字段
    required_fields = ['total_duration', 'fetch_duration', 'transform_duration', 
                      'update_duration', 'api_call_count', 'metrics']
    
    missing_fields = []
    for field in required_fields:
        if field not in performance:
            missing_fields.append(field)
    
    # 验证数据类型和值
    validations = {
        'api_call_count_type': isinstance(performance['api_call_count'], int),
        'api_call_count_value': performance['api_call_count'] == 15,
        'fetch_duration_type': isinstance(performance['fetch_duration'], (int, float)),
        'fetch_duration_value': performance['fetch_duration'] == 2.5,
        'metrics_type': isinstance(performance['metrics'], dict),
        'metrics_count': len(performance['metrics']) == 2,
        'no_missing_fields': len(missing_fields) == 0
    }
    
    print(f"\n验证结果:")
    for key, result in validations.items():
        print(f"  - {key}: {result}")
    
    if missing_fields:
        print(f"  - 缺失字段: {missing_fields}")
    
    all_valid = all(validations.values())
    
    if all_valid:
        print("✅ 性能统计字典结构测试通过!")
        return True
    else:
        print("❌ 性能统计字典结构测试失败!")
        return False


def main():
    """运行所有测试"""
    print("开始API调用统计简化测试...")
    print("=" * 80)
    
    tests = [
        test_sync_record_api_counting,
        test_api_call_timing_simulation,
        test_performance_dict_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 执行失败: {str(e)}")
    
    print("\n" + "=" * 80)
    print(f"测试总结: {passed}/{total} 通过")
    print("=" * 80)
    
    if passed == total:
        print("🎉 所有API调用统计测试通过!")
        print("API调用统计功能正常工作。")
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
    
    return passed == total


if __name__ == "__main__":
    main()
