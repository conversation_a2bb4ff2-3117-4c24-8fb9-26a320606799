#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计一致性测试脚本
测试source_statistics和statistics之间的一致性问题
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.record_config import SyncRecord

def test_statistics_consistency():
    """测试统计一致性"""
    print("=" * 60)
    print("测试统计一致性问题修复")
    print("=" * 60)
    
    # 创建测试同步记录
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    
    # 模拟数据处理阶段：添加总记录数
    sync_record.add_total_records(317)
    
    # 模拟数据源统计：在处理阶段记录total
    source_id = "source_1"
    for i in range(317):
        sync_record.add_source_statistic(source_id, 'total')
    
    print(f"1. 初始统计:")
    print(f"   - total_records: {sync_record.total_records}")
    print(f"   - source_statistics[{source_id}]['total']: {sync_record.source_statistics[source_id]['total']}")
    
    # 模拟应用sync_limits限制：假设只允许创建5个
    actual_created = 5
    excluded_count = 317 - actual_created
    
    # 模拟实际执行操作后的统计
    sync_record.add_success(actual_created, 'created', None)
    
    # 手动添加数据源统计（模拟批量操作成功后的统计）
    sync_record.add_source_statistic(source_id, 'success', actual_created)
    sync_record.add_source_statistic(source_id, 'created', actual_created)
    
    # 调整source_statistics中的total统计（模拟新的调整逻辑）
    sync_record.source_statistics[source_id]['total'] = max(
        0, sync_record.source_statistics[source_id]['total'] - excluded_count
    )
    
    print(f"\n2. 应用限制后的统计:")
    print(f"   - total_records: {sync_record.total_records}")
    print(f"   - created_count: {sync_record.created_count}")
    print(f"   - success_count: {sync_record.success_count}")
    print(f"   - source_statistics[{source_id}]: {sync_record.source_statistics[source_id]}")
    
    # 验证一致性
    source_stats = sync_record.source_statistics[source_id]
    
    print(f"\n3. 一致性检查:")
    
    # 检查总记录数一致性
    total_consistent = sync_record.total_records == source_stats['total']
    print(f"   - 总记录数一致性: {total_consistent} (total_records={sync_record.total_records}, source_total={source_stats['total']})")
    
    # 检查创建数一致性
    created_consistent = sync_record.created_count == source_stats['created']
    print(f"   - 创建数一致性: {created_consistent} (created_count={sync_record.created_count}, source_created={source_stats['created']})")
    
    # 检查成功数一致性
    success_consistent = sync_record.success_count == source_stats['success']
    print(f"   - 成功数一致性: {success_consistent} (success_count={sync_record.success_count}, source_success={source_stats['success']})")
    
    # 检查操作数量总和
    source_total_operations = source_stats['created'] + source_stats['updated'] + source_stats['deleted']
    main_total_operations = sync_record.created_count + sync_record.updated_count + sync_record.deleted_count
    operations_consistent = source_total_operations == main_total_operations
    print(f"   - 操作数量总和一致性: {operations_consistent} (source_ops={source_total_operations}, main_ops={main_total_operations})")
    
    # 总体一致性
    all_consistent = total_consistent and created_consistent and success_consistent and operations_consistent
    
    if all_consistent:
        print(f"\n✅ 统计一致性测试通过！")
        return True
    else:
        print(f"\n❌ 统计一致性测试失败！")
        return False

def test_multiple_sources_consistency():
    """测试多数据源的统计一致性"""
    print("\n" + "=" * 60)
    print("测试多数据源统计一致性")
    print("=" * 60)
    
    sync_record = SyncRecord("MULTI_SOURCE_MODEL", 'full', 'cloud_to_internal')
    
    # 模拟多个数据源
    sources_data = {
        "source_1": {"total": 200, "created": 3, "updated": 2},
        "source_2": {"total": 117, "created": 2, "updated": 1}
    }
    
    total_records = sum(data["total"] for data in sources_data.values())
    total_created = sum(data["created"] for data in sources_data.values())
    total_updated = sum(data["updated"] for data in sources_data.values())
    
    # 设置总记录数
    sync_record.add_total_records(total_records)
    
    # 模拟每个数据源的统计
    for source_id, data in sources_data.items():
        # 初始total统计
        for i in range(data["total"]):
            sync_record.add_source_statistic(source_id, 'total')
        
        # 应用限制后调整total
        excluded = data["total"] - data["created"] - data["updated"]
        sync_record.source_statistics[source_id]['total'] = max(
            0, sync_record.source_statistics[source_id]['total'] - excluded
        )
        
        # 实际操作统计
        sync_record.add_source_statistic(source_id, 'success', data["created"] + data["updated"])
        sync_record.add_source_statistic(source_id, 'created', data["created"])
        sync_record.add_source_statistic(source_id, 'updated', data["updated"])
    
    # 主统计
    sync_record.add_success(total_created, 'created', None)
    sync_record.add_success(total_updated, 'updated', None)
    
    print(f"多数据源统计结果:")
    print(f"  主统计:")
    print(f"    - total_records: {sync_record.total_records}")
    print(f"    - created_count: {sync_record.created_count}")
    print(f"    - updated_count: {sync_record.updated_count}")
    print(f"    - success_count: {sync_record.success_count}")
    
    print(f"  数据源统计:")
    for source_id in sources_data.keys():
        stats = sync_record.source_statistics[source_id]
        print(f"    - {source_id}: {stats}")
    
    # 验证一致性
    source_total_records = sum(stats['total'] for stats in sync_record.source_statistics.values())
    source_total_created = sum(stats['created'] for stats in sync_record.source_statistics.values())
    source_total_updated = sum(stats['updated'] for stats in sync_record.source_statistics.values())
    source_total_success = sum(stats['success'] for stats in sync_record.source_statistics.values())
    
    print(f"\n一致性检查:")
    print(f"  - 总记录数: {sync_record.total_records} == {source_total_records} ? {sync_record.total_records == source_total_records}")
    print(f"  - 创建数: {sync_record.created_count} == {source_total_created} ? {sync_record.created_count == source_total_created}")
    print(f"  - 更新数: {sync_record.updated_count} == {source_total_updated} ? {sync_record.updated_count == source_total_updated}")
    print(f"  - 成功数: {sync_record.success_count} == {source_total_success} ? {sync_record.success_count == source_total_success}")
    
    all_consistent = (
        sync_record.total_records == source_total_records and
        sync_record.created_count == source_total_created and
        sync_record.updated_count == source_total_updated and
        sync_record.success_count == source_total_success
    )
    
    if all_consistent:
        print(f"\n✅ 多数据源统计一致性测试通过！")
        return True
    else:
        print(f"\n❌ 多数据源统计一致性测试失败！")
        return False

if __name__ == "__main__":
    print("开始统计一致性测试...")
    
    # 测试单数据源一致性
    test1_result = test_statistics_consistency()
    
    # 测试多数据源一致性
    test2_result = test_multiple_sources_consistency()
    
    if test1_result and test2_result:
        print("\n🎉 所有统计一致性测试都通过了!")
        sys.exit(0)
    else:
        print("\n❌ 部分统计一致性测试失败!")
        sys.exit(1)
