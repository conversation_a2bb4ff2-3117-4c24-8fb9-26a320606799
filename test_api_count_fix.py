#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调用统计修复验证脚本
验证数据获取阶段的API调用是否被正确统计
"""

import sys
import os
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.record_config import SyncRecord

def test_api_count_timing():
    """测试API调用统计的时机"""
    print("=" * 60)
    print("测试API调用统计时机")
    print("=" * 60)
    
    # 模拟同步流程的不同阶段
    print("模拟同步流程:")
    
    # 阶段1: 创建同步记录（修复后：在数据获取前创建）
    print("1. 创建同步记录...")
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    current_sync_record = sync_record  # 模拟 _current_sync_record
    print(f"   初始API调用次数: {sync_record.api_call_count}")
    
    # 阶段2: 数据获取阶段（修复后：应该能统计API调用）
    print("2. 数据获取阶段...")
    
    # 模拟云端数据获取的多次API调用
    cloud_api_calls = 15  # 假设云端数据获取需要15次API调用
    print(f"   模拟云端数据获取: {cloud_api_calls}次API调用")
    for i in range(cloud_api_calls):
        if current_sync_record:  # 修复后：这里应该不为空
            current_sync_record.api_call_count += 1
        print(f"     云端API调用 {i+1}: 当前总次数 = {current_sync_record.api_call_count if current_sync_record else 'N/A'}")
    
    # 模拟内部数据获取的API调用
    internal_api_calls = 3  # 假设内部数据获取需要3次API调用
    print(f"   模拟内部数据获取: {internal_api_calls}次API调用")
    for i in range(internal_api_calls):
        if current_sync_record:
            current_sync_record.api_call_count += 1
        print(f"     内部API调用 {i+1}: 当前总次数 = {current_sync_record.api_call_count if current_sync_record else 'N/A'}")
    
    print(f"   数据获取阶段结束，总API调用次数: {sync_record.api_call_count}")
    
    # 阶段3: 数据同步阶段
    print("3. 数据同步阶段...")
    
    # 模拟同步操作的API调用
    sync_api_calls = 2  # 假设同步操作需要2次API调用
    print(f"   模拟同步操作: {sync_api_calls}次API调用")
    for i in range(sync_api_calls):
        if current_sync_record:
            current_sync_record.api_call_count += 1
        print(f"     同步API调用 {i+1}: 当前总次数 = {current_sync_record.api_call_count if current_sync_record else 'N/A'}")
    
    print(f"   数据同步阶段结束，总API调用次数: {sync_record.api_call_count}")
    
    # 验证结果
    expected_total = cloud_api_calls + internal_api_calls + sync_api_calls
    actual_total = sync_record.api_call_count
    
    print(f"\n结果验证:")
    print(f"   期望总API调用次数: {expected_total}")
    print(f"   实际总API调用次数: {actual_total}")
    print(f"   统计是否正确: {actual_total == expected_total}")
    
    if actual_total == expected_total:
        print("✅ API调用统计时机测试通过!")
        return True
    else:
        print("❌ API调用统计时机测试失败!")
        return False

def test_before_after_fix():
    """对比修复前后的差异"""
    print("\n" + "=" * 60)
    print("修复前后对比测试")
    print("=" * 60)
    
    print("修复前的问题场景:")
    print("1. sync_all_models() 开始")
    print("2. fetch_all_cloud_data() - _current_sync_record = None")
    print("3. fetch_all_internal_data() - _current_sync_record = None")
    print("4. sync_model() - 创建 sync_record，设置 _current_sync_record")
    print("5. 数据同步操作 - API调用被统计")
    print("结果: 只有同步阶段的API调用被统计，数据获取阶段的调用丢失")
    
    # 模拟修复前的情况
    print("\n模拟修复前:")
    current_sync_record_before = None  # 数据获取阶段为空
    
    # 数据获取阶段（API调用不被统计）
    cloud_calls_before = 15
    internal_calls_before = 3
    print(f"   数据获取阶段: {cloud_calls_before + internal_calls_before}次API调用")
    print(f"   统计结果: 0次 (因为 _current_sync_record 为空)")
    
    # 同步阶段（创建sync_record后，API调用被统计）
    sync_record_before = SyncRecord("TEST", 'full', 'cloud_to_internal')
    current_sync_record_before = sync_record_before
    sync_calls_before = 2
    for i in range(sync_calls_before):
        current_sync_record_before.api_call_count += 1
    print(f"   同步阶段: {sync_calls_before}次API调用")
    print(f"   统计结果: {sync_record_before.api_call_count}次")
    
    print(f"   修复前总统计: {sync_record_before.api_call_count}次 (实际应该是 {cloud_calls_before + internal_calls_before + sync_calls_before}次)")
    
    print("\n修复后的改进:")
    print("1. sync_all_models() 开始")
    print("2. 创建 sync_record，设置 _current_sync_record")
    print("3. fetch_all_cloud_data() - _current_sync_record 有效")
    print("4. fetch_all_internal_data() - _current_sync_record 有效")
    print("5. sync_model() - 使用已有的 sync_record")
    print("6. 数据同步操作 - API调用继续被统计")
    print("结果: 所有阶段的API调用都被正确统计")
    
    # 模拟修复后的情况
    print("\n模拟修复后:")
    sync_record_after = SyncRecord("TEST", 'full', 'cloud_to_internal')
    current_sync_record_after = sync_record_after  # 从一开始就有效
    
    # 数据获取阶段（API调用被统计）
    cloud_calls_after = 15
    internal_calls_after = 3
    for i in range(cloud_calls_after + internal_calls_after):
        current_sync_record_after.api_call_count += 1
    print(f"   数据获取阶段: {cloud_calls_after + internal_calls_after}次API调用")
    print(f"   统计结果: {sync_record_after.api_call_count}次")
    
    # 同步阶段（继续统计）
    sync_calls_after = 2
    for i in range(sync_calls_after):
        current_sync_record_after.api_call_count += 1
    print(f"   同步阶段: {sync_calls_after}次API调用")
    print(f"   统计结果: {sync_record_after.api_call_count}次")
    
    expected_after = cloud_calls_after + internal_calls_after + sync_calls_after
    print(f"   修复后总统计: {sync_record_after.api_call_count}次 (期望: {expected_after}次)")
    
    # 对比结果
    print(f"\n对比结果:")
    print(f"   修复前统计: {sync_record_before.api_call_count}次")
    print(f"   修复后统计: {sync_record_after.api_call_count}次")
    print(f"   改进效果: +{sync_record_after.api_call_count - sync_record_before.api_call_count}次")
    
    improvement_correct = (sync_record_after.api_call_count - sync_record_before.api_call_count) == (cloud_calls_after + internal_calls_after)
    
    if improvement_correct:
        print("✅ 修复前后对比测试通过!")
        return True
    else:
        print("❌ 修复前后对比测试失败!")
        return False

def analyze_real_scenario():
    """分析实际场景"""
    print("\n" + "=" * 60)
    print("实际场景分析")
    print("=" * 60)
    
    print("根据提供的日志分析:")
    print("- 日志显示有大量云端API调用（40+次）")
    print("- 但最终统计只有5次API调用")
    print("- 这符合修复前的问题特征")
    
    print("\n预期修复效果:")
    print("- 修复后，所有云端API调用都应该被统计")
    print("- 最终的 api_call_count 应该接近日志中显示的调用次数")
    print("- 应该能看到 'API调用统计: 云端API' 的日志记录")
    
    print("\n验证方法:")
    print("1. 运行修复后的代码")
    print("2. 检查日志中是否出现 'API调用统计: 云端API' 记录")
    print("3. 对比最终的 api_call_count 与日志中的实际调用次数")
    print("4. 确认统计结果的合理性")

if __name__ == "__main__":
    print("开始API调用统计修复验证...")
    
    # 运行测试
    test1_result = test_api_count_timing()
    test2_result = test_before_after_fix()
    
    # 分析实际场景
    analyze_real_scenario()
    
    if test1_result and test2_result:
        print("\n🎉 API调用统计修复验证通过!")
        print("修复应该能解决API调用统计不准确的问题。")
        sys.exit(0)
    else:
        print("\n❌ API调用统计修复验证失败!")
        sys.exit(1)
