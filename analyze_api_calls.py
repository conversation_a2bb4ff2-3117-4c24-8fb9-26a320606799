#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调用分析脚本
分析同步过程中的API调用是否合理
"""

def analyze_sync_scenario():
    """分析同步场景中的API调用"""
    print("=" * 60)
    print("同步场景API调用分析")
    print("=" * 60)
    
    # 根据提供的数据分析
    total_records = 317  # 云端总记录数
    created_count = 5    # 实际创建数量
    api_call_count = 5   # API调用次数
    
    print(f"场景数据:")
    print(f"  - 云端总记录数: {total_records}")
    print(f"  - 实际创建数量: {created_count}")
    print(f"  - API调用次数: {api_call_count}")
    print(f"  - 限制比例: {created_count/total_records*100:.1f}%")
    
    print(f"\n可能的API调用场景分析:")
    
    # 场景1: 数据获取阶段
    print(f"1. 数据获取阶段:")
    
    # 云端数据获取
    cloud_batch_size = 100  # 假设批次大小
    cloud_pages = (total_records + cloud_batch_size - 1) // cloud_batch_size
    print(f"   - 云端数据获取: {total_records}条记录，批次大小{cloud_batch_size}，需要{cloud_pages}次调用")
    
    # 内部数据获取
    internal_pages = 1  # 假设内部数据较少，1次调用
    print(f"   - 内部数据获取: 假设{internal_pages}次调用")
    
    # 关联数据获取
    related_calls = 0  # 假设没有关联数据
    print(f"   - 关联数据获取: {related_calls}次调用")
    
    fetch_total = cloud_pages + internal_pages + related_calls
    print(f"   - 数据获取小计: {fetch_total}次调用")
    
    # 场景2: 数据同步阶段
    print(f"\n2. 数据同步阶段:")
    
    # 批量创建
    batch_size = 30  # 假设批量操作大小
    create_batches = (created_count + batch_size - 1) // batch_size
    print(f"   - 批量创建: {created_count}条记录，批次大小{batch_size}，需要{create_batches}次调用")
    
    # 批量更新
    update_batches = 0  # 没有更新操作
    print(f"   - 批量更新: {update_batches}次调用")
    
    # 批量删除
    delete_batches = 0  # 没有删除操作
    print(f"   - 批量删除: {delete_batches}次调用")
    
    sync_total = create_batches + update_batches + delete_batches
    print(f"   - 数据同步小计: {sync_total}次调用")
    
    # 场景3: 其他调用
    print(f"\n3. 其他调用:")
    auth_calls = 0  # 认证调用通常不计入
    other_calls = 0  # 其他辅助调用
    other_total = auth_calls + other_calls
    print(f"   - 认证调用: {auth_calls}次")
    print(f"   - 其他调用: {other_calls}次")
    print(f"   - 其他调用小计: {other_total}次")
    
    # 总计分析
    expected_total = fetch_total + sync_total + other_total
    print(f"\n总计分析:")
    print(f"   - 预期总调用次数: {expected_total}次")
    print(f"   - 实际调用次数: {api_call_count}次")
    print(f"   - 差异: {api_call_count - expected_total}次")
    
    # 分析结论
    print(f"\n分析结论:")
    if api_call_count == expected_total:
        print(f"✅ API调用次数合理")
    elif api_call_count < expected_total:
        print(f"⚠️  API调用次数偏少，可能原因:")
        print(f"     - 数据获取使用了更大的批次大小")
        print(f"     - 某些调用被缓存或跳过")
        print(f"     - 统计逻辑有遗漏")
    else:
        print(f"⚠️  API调用次数偏多，可能原因:")
        print(f"     - 存在重试调用")
        print(f"     - 有额外的辅助调用")
        print(f"     - 批次大小较小")
    
    return expected_total, api_call_count

def analyze_different_scenarios():
    """分析不同场景下的API调用"""
    print("\n" + "=" * 60)
    print("不同场景API调用分析")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "场景1: 小批次同步",
            "total_records": 317,
            "created": 5,
            "cloud_batch_size": 50,
            "internal_batch_size": 100,
            "sync_batch_size": 1,  # 逐条同步
        },
        {
            "name": "场景2: 中批次同步",
            "total_records": 317,
            "created": 5,
            "cloud_batch_size": 100,
            "internal_batch_size": 200,
            "sync_batch_size": 5,  # 5条一批
        },
        {
            "name": "场景3: 大批次同步",
            "total_records": 317,
            "created": 5,
            "cloud_batch_size": 200,
            "internal_batch_size": 500,
            "sync_batch_size": 30,  # 30条一批
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        
        # 计算各阶段调用次数
        cloud_calls = (scenario['total_records'] + scenario['cloud_batch_size'] - 1) // scenario['cloud_batch_size']
        internal_calls = 1  # 假设内部数据1次获取完成
        sync_calls = (scenario['created'] + scenario['sync_batch_size'] - 1) // scenario['sync_batch_size']
        
        total_calls = cloud_calls + internal_calls + sync_calls
        
        print(f"   - 云端数据获取: {cloud_calls}次 (批次大小: {scenario['cloud_batch_size']})")
        print(f"   - 内部数据获取: {internal_calls}次")
        print(f"   - 同步操作: {sync_calls}次 (批次大小: {scenario['sync_batch_size']})")
        print(f"   - 总计: {total_calls}次")
        
        if total_calls == 5:
            print(f"   ✅ 此场景下API调用次数为5次，符合实际结果")
        else:
            print(f"   ❌ 此场景下API调用次数为{total_calls}次，不符合实际结果")

def recommend_investigation():
    """推荐调查方向"""
    print("\n" + "=" * 60)
    print("调查建议")
    print("=" * 60)
    
    print("为了准确分析API调用次数，建议:")
    print()
    print("1. 检查日志中的API调用记录:")
    print("   - 查找 'API调用统计: 内部API' 的日志")
    print("   - 查找 'API调用统计: 云端API' 的日志")
    print("   - 统计每种类型的调用次数")
    print()
    print("2. 检查配置参数:")
    print("   - 云端数据获取的批次大小 (batch_size)")
    print("   - 内部数据获取的批次大小")
    print("   - 同步操作的批次大小")
    print("   - 是否启用了分页")
    print()
    print("3. 检查同步限制:")
    print("   - sync_limits 配置")
    print("   - 是否有操作被跳过")
    print("   - 是否有重试逻辑")
    print()
    print("4. 验证统计逻辑:")
    print("   - 确认所有API调用都被统计")
    print("   - 确认没有重复统计")
    print("   - 确认认证调用是否应该计入")

if __name__ == "__main__":
    print("开始API调用分析...")
    
    # 分析当前场景
    expected, actual = analyze_sync_scenario()
    
    # 分析不同场景
    analyze_different_scenarios()
    
    # 推荐调查方向
    recommend_investigation()
    
    print(f"\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
