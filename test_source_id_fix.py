#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试_source_id修复效果的脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.record_config import SyncRecord

def test_source_id_in_items():
    """测试项目中是否包含_source_id字段"""
    print("=" * 60)
    print("测试_source_id字段传递")
    print("=" * 60)
    
    # 模拟批量操作中的项目
    batch_items = [
        {
            'id': '1',
            'name': 'test1',
            '_source_id': 'source_1'
        },
        {
            'id': '2', 
            'name': 'test2',
            '_source_id': 'source_1'
        },
        {
            'id': '3',
            'name': 'test3',
            '_source_id': 'source_2'
        }
    ]
    
    # 模拟批量操作中的统计逻辑
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    operation_type = 'created'
    
    # 统计数据源
    source_counts = {}
    for item in batch_items:
        source_id = item.get('_source_id')
        print(f"Processing item {item['id']} with source_id: {source_id}")
        if source_id:
            source_counts[source_id] = source_counts.get(source_id, 0) + 1
        else:
            print(f"WARNING: Item {item['id']} missing _source_id")
    
    print(f"\nSource counts: {source_counts}")
    
    # 更新统计
    sync_record.add_success(len(batch_items), operation_type, None)
    
    # 添加数据源统计
    for source_id, count in source_counts.items():
        print(f"Adding source statistic: {source_id}, success: {count}, {operation_type}: {count}")
        sync_record.add_source_statistic(source_id, 'success', count)
        sync_record.add_source_statistic(source_id, operation_type, count)
    
    # 输出结果
    print(f"\n主统计:")
    print(f"  - success_count: {sync_record.success_count}")
    print(f"  - created_count: {sync_record.created_count}")
    
    print(f"\n数据源统计:")
    for source_id, stats in sync_record.source_statistics.items():
        print(f"  - {source_id}: {stats}")
    
    # 验证一致性
    total_source_success = sum(stats['success'] for stats in sync_record.source_statistics.values())
    total_source_created = sum(stats['created'] for stats in sync_record.source_statistics.values())
    
    success_consistent = sync_record.success_count == total_source_success
    created_consistent = sync_record.created_count == total_source_created
    
    print(f"\n一致性检查:")
    print(f"  - 成功数一致性: {success_consistent} ({sync_record.success_count} == {total_source_success})")
    print(f"  - 创建数一致性: {created_consistent} ({sync_record.created_count} == {total_source_created})")
    
    if success_consistent and created_consistent:
        print(f"\n✅ 统计一致性测试通过!")
        return True
    else:
        print(f"\n❌ 统计一致性测试失败!")
        return False

def test_missing_source_id():
    """测试缺少_source_id的情况"""
    print("\n" + "=" * 60)
    print("测试缺少_source_id的情况")
    print("=" * 60)
    
    # 模拟缺少_source_id的项目
    batch_items = [
        {
            'id': '1',
            'name': 'test1'
            # 缺少_source_id
        },
        {
            'id': '2', 
            'name': 'test2',
            '_source_id': 'source_1'
        }
    ]
    
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    operation_type = 'created'
    
    # 统计数据源
    source_counts = {}
    items_without_source = 0
    
    for item in batch_items:
        source_id = item.get('_source_id')
        print(f"Processing item {item['id']} with source_id: {source_id}")
        if source_id:
            source_counts[source_id] = source_counts.get(source_id, 0) + 1
        else:
            items_without_source += 1
            print(f"WARNING: Item {item['id']} missing _source_id")
    
    print(f"\nSource counts: {source_counts}")
    print(f"Items without source_id: {items_without_source}")
    
    # 更新统计
    sync_record.add_success(len(batch_items), operation_type, None)
    
    # 添加数据源统计
    for source_id, count in source_counts.items():
        sync_record.add_source_statistic(source_id, 'success', count)
        sync_record.add_source_statistic(source_id, operation_type, count)
    
    # 输出结果
    print(f"\n主统计:")
    print(f"  - success_count: {sync_record.success_count}")
    print(f"  - created_count: {sync_record.created_count}")
    
    print(f"\n数据源统计:")
    for source_id, stats in sync_record.source_statistics.items():
        print(f"  - {source_id}: {stats}")
    
    # 在这种情况下，主统计应该包含所有项目，但数据源统计只包含有source_id的项目
    total_source_success = sum(stats['success'] for stats in sync_record.source_statistics.values())
    
    print(f"\n分析:")
    print(f"  - 主统计成功数: {sync_record.success_count}")
    print(f"  - 数据源统计成功数总和: {total_source_success}")
    print(f"  - 缺少source_id的项目数: {items_without_source}")
    print(f"  - 预期差异: {sync_record.success_count - total_source_success} (应该等于缺少source_id的项目数)")
    
    expected_diff = items_without_source
    actual_diff = sync_record.success_count - total_source_success
    
    if actual_diff == expected_diff:
        print(f"\n✅ 缺少source_id处理测试通过!")
        return True
    else:
        print(f"\n❌ 缺少source_id处理测试失败!")
        return False

if __name__ == "__main__":
    print("开始_source_id修复效果测试...")
    
    # 测试正常情况
    test1_result = test_source_id_in_items()
    
    # 测试缺少source_id的情况
    test2_result = test_missing_source_id()
    
    if test1_result and test2_result:
        print("\n🎉 所有_source_id相关测试都通过了!")
        sys.exit(0)
    else:
        print("\n❌ 部分_source_id相关测试失败!")
        sys.exit(1)
