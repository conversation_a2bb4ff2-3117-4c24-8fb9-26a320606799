#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调用统计测试脚本
测试API调用次数统计的准确性，包括分页、重试等情况
"""

import sys
import os
import time
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.sync_manager import SyncManager
from src.record_config import SyncRecord

def test_api_call_count_basic():
    """测试基本的API调用统计"""
    print("=" * 60)
    print("测试基本API调用统计")
    print("=" * 60)
    
    # 创建同步记录
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    
    # 模拟API调用
    print("模拟5次API调用...")
    for i in range(5):
        sync_record.api_call_count += 1
        sync_record.add_performance_metric(f"api_call_test_{i}_duration", 0.1 + i * 0.05)
    
    print(f"API调用次数: {sync_record.api_call_count}")
    print(f"性能指标: {sync_record.performance_metrics}")
    
    # 验证
    expected_count = 5
    if sync_record.api_call_count == expected_count:
        print(f"✅ 基本API调用统计测试通过! (期望: {expected_count}, 实际: {sync_record.api_call_count})")
        return True
    else:
        print(f"❌ 基本API调用统计测试失败! (期望: {expected_count}, 实际: {sync_record.api_call_count})")
        return False

def test_api_call_count_with_pagination():
    """测试分页情况下的API调用统计"""
    print("\n" + "=" * 60)
    print("测试分页API调用统计")
    print("=" * 60)
    
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    
    # 模拟分页调用：假设有3页数据，每页需要1次API调用
    pages = 3
    calls_per_page = 1
    
    print(f"模拟分页调用: {pages}页，每页{calls_per_page}次调用...")
    
    for page in range(1, pages + 1):
        for call in range(calls_per_page):
            sync_record.api_call_count += 1
            sync_record.add_performance_metric(f"api_call_page_{page}_call_{call}_duration", 0.2)
        print(f"  页面 {page}: 调用次数 +{calls_per_page}")
    
    expected_total = pages * calls_per_page
    print(f"\n总API调用次数: {sync_record.api_call_count}")
    print(f"期望调用次数: {expected_total}")
    
    if sync_record.api_call_count == expected_total:
        print(f"✅ 分页API调用统计测试通过!")
        return True
    else:
        print(f"❌ 分页API调用统计测试失败!")
        return False

def test_api_call_count_with_retry():
    """测试重试情况下的API调用统计"""
    print("\n" + "=" * 60)
    print("测试重试API调用统计")
    print("=" * 60)
    
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    
    # 模拟重试场景：
    # - 第1次调用失败，重试2次后成功 = 3次调用
    # - 第2次调用成功 = 1次调用
    # - 第3次调用失败，重试1次后成功 = 2次调用
    # 总计：6次调用
    
    scenarios = [
        {"name": "第1次调用(失败+2次重试)", "calls": 3},
        {"name": "第2次调用(成功)", "calls": 1},
        {"name": "第3次调用(失败+1次重试)", "calls": 2}
    ]
    
    total_expected = 0
    for scenario in scenarios:
        print(f"模拟 {scenario['name']}: {scenario['calls']}次调用")
        for i in range(scenario['calls']):
            sync_record.api_call_count += 1
            sync_record.add_performance_metric(f"api_call_{scenario['name']}_attempt_{i}_duration", 0.15)
        total_expected += scenario['calls']
    
    print(f"\n总API调用次数: {sync_record.api_call_count}")
    print(f"期望调用次数: {total_expected}")
    
    if sync_record.api_call_count == total_expected:
        print(f"✅ 重试API调用统计测试通过!")
        return True
    else:
        print(f"❌ 重试API调用统计测试失败!")
        return False

def test_api_call_count_mixed_scenario():
    """测试混合场景下的API调用统计"""
    print("\n" + "=" * 60)
    print("测试混合场景API调用统计")
    print("=" * 60)
    
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    
    # 混合场景：
    # 1. 获取云端数据：3页，每页1次调用 = 3次
    # 2. 获取内部数据：2页，每页1次调用 = 2次
    # 3. 批量更新：1次调用，但重试1次 = 2次
    # 4. 批量创建：1次调用，成功 = 1次
    # 5. 批量删除：1次调用，成功 = 1次
    # 总计：9次调用
    
    operations = [
        {"name": "获取云端数据", "calls": 3, "type": "cloud_fetch"},
        {"name": "获取内部数据", "calls": 2, "type": "internal_fetch"},
        {"name": "批量更新(含重试)", "calls": 2, "type": "batch_update"},
        {"name": "批量创建", "calls": 1, "type": "batch_create"},
        {"name": "批量删除", "calls": 1, "type": "batch_delete"}
    ]
    
    total_expected = 0
    for op in operations:
        print(f"模拟 {op['name']}: {op['calls']}次调用")
        for i in range(op['calls']):
            sync_record.api_call_count += 1
            sync_record.add_performance_metric(f"api_call_{op['type']}_{i}_duration", 0.1 + i * 0.02)
        total_expected += op['calls']
    
    print(f"\n总API调用次数: {sync_record.api_call_count}")
    print(f"期望调用次数: {total_expected}")
    print(f"性能指标数量: {len(sync_record.performance_metrics)}")
    
    # 验证API调用次数
    count_correct = sync_record.api_call_count == total_expected
    
    # 验证性能指标数量（应该等于API调用次数）
    metrics_correct = len(sync_record.performance_metrics) == total_expected
    
    print(f"\n验证结果:")
    print(f"  - API调用次数正确: {count_correct}")
    print(f"  - 性能指标数量正确: {metrics_correct}")
    
    if count_correct and metrics_correct:
        print(f"✅ 混合场景API调用统计测试通过!")
        return True
    else:
        print(f"❌ 混合场景API调用统计测试失败!")
        return False

def test_sync_record_to_dict():
    """测试同步记录转换为字典时API统计的完整性"""
    print("\n" + "=" * 60)
    print("测试同步记录字典转换")
    print("=" * 60)
    
    sync_record = SyncRecord("TEST_MODEL", 'full', 'cloud_to_internal')
    
    # 添加一些API调用统计
    sync_record.api_call_count = 8
    sync_record.add_performance_metric("api_call_get_data_duration", 0.5)
    sync_record.add_performance_metric("api_call_update_data_duration", 1.2)
    sync_record.add_performance_metric("api_call_delete_data_duration", 0.3)
    
    # 完成同步记录
    sync_record.complete('success')
    
    # 转换为字典
    record_dict = sync_record.to_dict()
    
    print(f"API调用次数: {record_dict['performance']['api_call_count']}")
    print(f"性能指标: {record_dict['performance']['metrics']}")
    print(f"总耗时: {record_dict['performance']['total_duration']}")
    
    # 验证
    api_count_correct = record_dict['performance']['api_call_count'] == 8
    metrics_count_correct = len(record_dict['performance']['metrics']) == 3
    has_total_duration = record_dict['performance']['total_duration'] > 0
    
    print(f"\n验证结果:")
    print(f"  - API调用次数正确: {api_count_correct}")
    print(f"  - 性能指标数量正确: {metrics_count_correct}")
    print(f"  - 总耗时大于0: {has_total_duration}")
    
    if api_count_correct and metrics_count_correct and has_total_duration:
        print(f"✅ 同步记录字典转换测试通过!")
        return True
    else:
        print(f"❌ 同步记录字典转换测试失败!")
        return False

if __name__ == "__main__":
    print("开始API调用统计测试...")
    
    # 运行所有测试
    tests = [
        test_api_call_count_basic,
        test_api_call_count_with_pagination,
        test_api_call_count_with_retry,
        test_api_call_count_mixed_scenario,
        test_sync_record_to_dict
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 发生异常: {str(e)}")
            results.append(False)
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 60)
    print(f"测试总结: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有API调用统计测试都通过了!")
        sys.exit(0)
    else:
        print("❌ 部分API调用统计测试失败!")
        sys.exit(1)
