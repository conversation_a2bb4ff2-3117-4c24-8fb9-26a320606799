#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能统计功能测试脚本
测试云内向行内同步流程中的性能记录功能
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.sync_manager import SyncManager
from src.record_config import SyncRecord, SyncRecordManager
from src.config_loader import cmdb_config

def test_performance_stats():
    """测试性能统计功能"""
    print("=" * 60)
    print("开始测试性能统计功能")
    print("=" * 60)
    
    try:
        # 初始化同步管理器
        print("1. 初始化同步管理器...")
        sync_manager = SyncManager()
        
        # 创建测试用的同步记录
        print("2. 创建测试同步记录...")
        test_model_id = "TEST_MODEL"
        sync_record = SyncRecord(test_model_id, 'full', 'cloud_to_internal')
        
        # 设置当前同步记录
        sync_manager._current_sync_record = sync_record
        
        # 测试性能计时器
        print("3. 测试性能计时器...")
        
        # 测试数据获取性能统计
        fetch_start = sync_manager._start_performance_timer("test_fetch")
        time.sleep(0.1)  # 模拟数据获取耗时
        sync_manager._end_performance_timer(fetch_start, "fetch", sync_record)
        
        # 测试数据转换性能统计
        transform_start = sync_manager._start_performance_timer("test_transform")
        time.sleep(0.05)  # 模拟数据转换耗时
        sync_manager._end_performance_timer(transform_start, "transform", sync_record)
        
        # 测试数据更新性能统计
        update_start = sync_manager._start_performance_timer("test_update")
        time.sleep(0.08)  # 模拟数据更新耗时
        sync_manager._end_performance_timer(update_start, "update", sync_record)
        
        # 测试自定义性能指标
        sync_record.add_performance_metric("custom_metric_1", 1.23)
        sync_record.add_performance_metric("custom_metric_2", 4.56)
        
        # 模拟API调用统计
        sync_record.api_call_count = 5
        
        # 完成同步记录
        sync_record.complete('success')
        
        # 输出性能统计结果
        print("4. 性能统计结果:")
        print(f"   - 总耗时: {sync_record.total_duration:.3f}s")
        print(f"   - 数据获取耗时: {sync_record.fetch_duration:.3f}s")
        print(f"   - 数据转换耗时: {sync_record.transform_duration:.3f}s")
        print(f"   - 数据更新耗时: {sync_record.update_duration:.3f}s")
        print(f"   - API调用次数: {sync_record.api_call_count}")
        print(f"   - 自定义性能指标: {sync_record.performance_metrics}")
        
        # 测试同步记录保存
        print("5. 测试同步记录保存...")
        record_manager = SyncRecordManager()
        record_manager.save_record(sync_record)
        
        # 验证保存的记录
        print("6. 验证保存的记录...")
        saved_record = record_manager.get_record(sync_record.id)
        if saved_record:
            performance_data = saved_record.get('performance', {})
            print(f"   - 保存的总耗时: {performance_data.get('total_duration', 0):.3f}s")
            print(f"   - 保存的获取耗时: {performance_data.get('fetch_duration', 0):.3f}s")
            print(f"   - 保存的转换耗时: {performance_data.get('transform_duration', 0):.3f}s")
            print(f"   - 保存的更新耗时: {performance_data.get('update_duration', 0):.3f}s")
            print(f"   - 保存的API调用次数: {performance_data.get('api_call_count', 0)}")
            print(f"   - 保存的自定义指标: {performance_data.get('metrics', {})}")
            
            # 验证性能数据是否正确保存
            assert performance_data.get('fetch_duration', 0) > 0, "获取耗时应该大于0"
            assert performance_data.get('transform_duration', 0) > 0, "转换耗时应该大于0"
            assert performance_data.get('update_duration', 0) > 0, "更新耗时应该大于0"
            assert performance_data.get('api_call_count', 0) == 5, "API调用次数应该为5"
            assert 'custom_metric_1' in performance_data.get('metrics', {}), "应该包含自定义指标1"
            assert 'custom_metric_2' in performance_data.get('metrics', {}), "应该包含自定义指标2"
            
            print("   ✓ 所有性能数据验证通过!")
        else:
            print("   ✗ 未能获取保存的记录")
            return False
        
        print("=" * 60)
        print("性能统计功能测试完成 - 所有测试通过!")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_decorator():
    """测试性能装饰器功能"""
    print("\n" + "=" * 60)
    print("测试性能装饰器功能")
    print("=" * 60)
    
    try:
        # 创建一个测试类来验证装饰器
        from src.sync_manager import performance_timer
        
        class TestClass:
            def __init__(self):
                self._current_sync_record = SyncRecord("TEST", 'full', 'cloud_to_internal')
            
            @performance_timer("test_operation")
            def test_method(self):
                time.sleep(0.1)  # 模拟操作耗时
                return "success"
        
        test_obj = TestClass()
        result = test_obj.test_method()
        
        # 检查性能指标是否被记录
        metrics = test_obj._current_sync_record.performance_metrics
        print(f"装饰器记录的性能指标: {metrics}")
        
        assert 'test_operation_duration' in metrics, "应该记录操作耗时"
        assert metrics['test_operation_duration'] > 0, "操作耗时应该大于0"
        
        print("✓ 性能装饰器测试通过!")
        return True
        
    except Exception as e:
        print(f"装饰器测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始性能统计功能测试...")
    
    # 测试基本性能统计功能
    test1_result = test_performance_stats()
    
    # 测试性能装饰器功能
    test2_result = test_performance_decorator()
    
    if test1_result and test2_result:
        print("\n🎉 所有测试都通过了!")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败!")
        sys.exit(1)
