import os
import uuid
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import mysql.connector
from mysql.connector import Error

from src.config_loader import cmdb_config
from src.cipher.cipher_util import CipherUtil
# from src.logger_config import logger
import logging
#
# # 确保日志配置已设置
# setup_logging()
logger = logging.getLogger(__name__)

class SyncRecord:
    """同步记录类，用于记录同步过程中的数据"""
    def __init__(self, model_id, sync_type, sync_direction):
        self.id = str(uuid.uuid4())
        self.model_id = model_id
        # 'full' or 'incremental'
        self.sync_type = sync_type
        # 'cloud_to_internal', 'internal_to_cloud', 'bidirectional'
        self.sync_direction = sync_direction
        self.start_time = datetime.now()
        self.end_time = None
        # 'in_progress', 'success', 'failed', 'partial_success'
        self.status = 'in_progress'
        # 数据源ID
        self.source_id = None
        
        # 数据统计
        self.total_records = 0
        self.success_count = 0
        self.failed_count = 0
        self.skipped_count = 0
        self.created_count = 0
        self.updated_count = 0
        self.deleted_count = 0

        # 按方向区分的数据量统计
        self.cloud_to_internal_count = 0
        self.internal_to_cloud_count = 0

        # 按数据源区分的统计
        self.source_statistics = {}  # 格式: {source_id: {total: 0, success: 0, ...}}

        # 性能统计
        self.total_duration = 0
        self.fetch_duration = 0
        self.transform_duration = 0
        self.update_duration = 0
        self.api_call_count = 0
        self.performance_metrics = {}

        # 错误信息
        self.errors = []
        # 变更记录
        self.changes = []
    
    def add_performance_metric(self, metric_name, value):
        """添加性能指标"""
        self.performance_metrics[metric_name] = value

    def add_error(self, error_type, error_message, record_id=None, field=None, stack_trace=None):
        """添加错误信息"""
        self.errors.append({
            'type': error_type,
            'message': error_message,
            'record_id': record_id,
            'field': field,
            'time': datetime.now(),
            'stack_trace': stack_trace
        })
    
    def add_change(self, record_id, field ,old_record, new_record, operation_type):
        """添加记录级变更

        Args:
            record_id: 记录ID
            field: 字段名称，整条记录保存时，这个字段为空（向前兼容）
            old_record: 旧记录数据（字典或None）
            new_record: 新记录数据（字典或None）
            operation_type: 操作类型（created/updated/deleted）
        """
        self.changes.append({
            'record_id': record_id,
            'operation_type': operation_type,  # 操作类型
            'field': field,                    # 字段名称
            'old_value': old_record,           # 完整的旧记录
            'new_value': new_record,           # 完整的新记录
            'time': datetime.now()
        })
    
    def add_source_statistic(self, source_id, stat_type, count=1):
        """添加数据源统计"""
        if source_id:
            if source_id not in self.source_statistics:
                self.source_statistics[source_id] = {
                    'total': 0,
                    'success': 0,
                    'failed': 0,
                    'skipped': 0,
                    'created': 0,
                    'updated': 0,
                    'deleted': 0
                }

            if stat_type in self.source_statistics[source_id]:
                self.source_statistics[source_id][stat_type] += count

    def add_total_records(self, count):
        """添加总记录数"""
        self.total_records += count

    def add_success(self, count, operation_type=None, source_id=None):
        """添加成功记录

        Args:
            count: 成功记录数量
            operation_type: 操作类型 ('created', 'updated', 'deleted')
            source_id: 数据源ID（可选）
        """
        self.success_count += count

        if operation_type == 'created':
            self.created_count += count
        elif operation_type == 'updated':
            self.updated_count += count
        elif operation_type == 'deleted':
            self.deleted_count += count

        # 数据源统计（如果提供了source_id）
        if source_id:
            self.add_source_statistic(source_id, 'success', count)
            if operation_type:
                self.add_source_statistic(source_id, operation_type, count)

    def add_failed(self, count, source_id=None):
        """添加失败记录"""
        self.failed_count += count
        if source_id:
            self.add_source_statistic(source_id, 'failed', count)

    def add_skipped(self, count, source_id=None):
        """添加跳过记录"""
        self.skipped_count += count
        if source_id:
            self.add_source_statistic(source_id, 'skipped', count)

    def validate_counts(self):
        """验证计数的一致性"""
        calculated_total = self.success_count + self.failed_count + self.skipped_count
        if 0 < self.total_records != calculated_total:
            logger.warning(f"Count mismatch: total_records={self.total_records}, "
                         f"calculated_total={calculated_total} "
                         f"(success={self.success_count}, failed={self.failed_count}, skipped={self.skipped_count})")
            return False
        return True

    def get_summary(self):
        """获取统计摘要"""
        return {
            'total_records': self.total_records,
            'success_count': self.success_count,
            'failed_count': self.failed_count,
            'skipped_count': self.skipped_count,
            'created_count': self.created_count,
            'updated_count': self.updated_count,
            'deleted_count': self.deleted_count,
            'success_rate': (self.success_count / max(self.total_records, 1)) * 100,
            'is_consistent': self.validate_counts()
        }

    def complete(self, status='success'):
        """完成同步记录"""
        self.end_time = datetime.now()
        self.status = status
        self.total_duration = (self.end_time - self.start_time).total_seconds()
        return self
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'model_id': self.model_id,
            'sync_type': self.sync_type,
            'sync_direction': self.sync_direction,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'source_id': self.source_id,
            'statistics': {
                'total_records': self.total_records,
                'success_count': self.success_count,
                'failed_count': self.failed_count,
                'skipped_count': self.skipped_count,
                'created_count': self.created_count,
                'updated_count': self.updated_count,
                'deleted_count': self.deleted_count,
                'cloud_to_internal_count': self.cloud_to_internal_count,
                'internal_to_cloud_count': self.internal_to_cloud_count,
                'success_rate': (self.success_count / max(self.total_records, 1)) * 100,
                'is_consistent': self.validate_counts()
            },
            'source_statistics': self.source_statistics,
            'errors': self.errors,
            'performance': {
                'total_duration': self.total_duration,
                'fetch_duration': self.fetch_duration,
                'transform_duration': self.transform_duration,
                'update_duration': self.update_duration,
                'api_call_count': self.api_call_count,
                'metrics': self.performance_metrics
            },
            'changes': self.changes if len(self.changes) <= 100 else self.changes[:100]  # 限制变更详情数量
        }


class SyncRecordManager:
    """同步记录管理器，负责保存和查询同步记录"""
    
    def __init__(self):
        """初始化同步记录管理器"""
        self.config = cmdb_config.get('base', {}).get('sync_records', {})
        self.enabled = self.config.get('enabled', True)
        self.max_records = self.config.get('max_records', 1000)
        self.retention_days = self.config.get('retention_days', 30)
        self.detail_level = self.config.get('detail_level', 'standard')
        self.storage_type = self.config.get('storage_type', 'database')  # 'file' or 'database'
        
        # 数据库配置
        self.db_config = cmdb_config.get('base', {}).get('database', {})
        self.host = self.db_config.get('host_name')
        self.user = self.db_config.get('user_name')
        self.password = CipherUtil.decrypt(self.db_config.get('password', ''))
        self.database = self.db_config.get('db_name')
        self.port = self.db_config.get('port', 3306)
        
        # 初始化数据库表
        if self.storage_type == 'database' and self.enabled:

            self._init_database()

            # 执行执行迁移记录
            self.migrator = DatabaseMigrator(self.host, self.user, self.password, self.database, self.port)
            self.migrator.run_migrations()

    
    def _init_database(self):
        """初始化数据库表"""
        try:
            conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port
            )
            
            cursor = conn.cursor()
            
            # 创建同步记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_records (
                    id VARCHAR(36) PRIMARY KEY,
                    model_id VARCHAR(100) NOT NULL,
                    sync_type VARCHAR(20) NOT NULL,
                    sync_direction VARCHAR(30) NOT NULL,
                    start_time DATETIME NOT NULL,
                    end_time DATETIME,
                    status VARCHAR(20) NOT NULL,
                    source_id VARCHAR(100),
                    total_records INT DEFAULT 0,
                    success_count INT DEFAULT 0,
                    failed_count INT DEFAULT 0,
                    skipped_count INT DEFAULT 0,
                    created_count INT DEFAULT 0,
                    updated_count INT DEFAULT 0,
                    deleted_count INT DEFAULT 0,
                    total_duration FLOAT DEFAULT 0,
                    fetch_duration FLOAT DEFAULT 0,
                    transform_duration FLOAT DEFAULT 0,
                    update_duration FLOAT DEFAULT 0,
                    api_call_count INT DEFAULT 0,
                    cloud_to_internal_count INT DEFAULT 0,
                    internal_to_cloud_count INT DEFAULT 0,
                    source_statistics JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建错误记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_errors (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    sync_id VARCHAR(36) NOT NULL,
                    error_type VARCHAR(50) NOT NULL,
                    error_message TEXT NOT NULL,
                    error_time DATETIME NOT NULL,
                    record_id VARCHAR(100),
                    stack_trace TEXT,
                    FOREIGN KEY (sync_id) REFERENCES sync_records(id) ON DELETE CASCADE
                )
            ''')
            
            # 创建变更记录表（如果详细级别为detailed）
            if self.detail_level == 'detailed':
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sync_changes (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        sync_id VARCHAR(36) NOT NULL,
                        record_id VARCHAR(100) NOT NULL,
                        field VARCHAR(100) NOT NULL,
                        old_value TEXT,
                        new_value TEXT,
                        change_time DATETIME NOT NULL,
                        FOREIGN KEY (sync_id) REFERENCES sync_records(id) ON DELETE CASCADE
                    )
                ''')

                # # 检查 operation_type 字段是否存在
                # cursor.execute("""
                #     SELECT COUNT(*)
                #     FROM information_schema.columns
                #     WHERE table_schema = %s
                #       AND table_name = 'sync_changes'
                #       AND column_name = 'operation_type'
                # """, (self.database,))  # 注意这里要用 (xxx,) 才是 tuple
                # exists = cursor.fetchone()[0]
                #
                # if exists == 0:
                #     cursor.execute('''
                #         ALTER TABLE sync_changes
                #         ADD COLUMN operation_type VARCHAR(50) COMMENT '操作类型：created/updated/deleted' AFTER record_id
                #      ''')

            # 创建性能指标表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_performance_metrics (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    sync_id VARCHAR(36) NOT NULL,
                    metric_name VARCHAR(100) NOT NULL,
                    metric_value FLOAT NOT NULL,
                    FOREIGN KEY (sync_id) REFERENCES sync_records(id) ON DELETE CASCADE
                )
            ''')

            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info("Sync record database tables initialized successfully")
        except Error as e:
            logger.error(f"Error initializing sync record database: {str(e)}")
    
    def save_record(self, sync_record: SyncRecord):
        """保存同步记录"""
        if not self.enabled:
            return
        
        if self.storage_type == 'database':
            self._save_to_database(sync_record)
        else:
            self._save_to_file(sync_record)
    
    def _save_to_database(self, sync_record: SyncRecord):
        """将同步记录保存到数据库"""
        try:
            conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port
            )
            
            cursor = conn.cursor()
            
            # 保存主记录
            cursor.execute('''
                INSERT INTO sync_records (
                    id, model_id, sync_type, sync_direction, start_time, end_time, status, source_id,
                    total_records, success_count, failed_count, skipped_count, created_count, updated_count, deleted_count,
                    total_duration, fetch_duration, transform_duration, update_duration, api_call_count,
                    cloud_to_internal_count, internal_to_cloud_count, source_statistics
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s
                )
            ''', (
                sync_record.id, sync_record.model_id, sync_record.sync_type, sync_record.sync_direction,
                sync_record.start_time, sync_record.end_time, sync_record.status, sync_record.source_id,
                sync_record.total_records, sync_record.success_count, sync_record.failed_count,
                sync_record.skipped_count, sync_record.created_count, sync_record.updated_count, sync_record.deleted_count,
                sync_record.total_duration, sync_record.fetch_duration, sync_record.transform_duration,
                sync_record.update_duration, sync_record.api_call_count,
                sync_record.cloud_to_internal_count, sync_record.internal_to_cloud_count,
                json.dumps(sync_record.source_statistics)
            ))
            
            # 保存错误记录
            for error in sync_record.errors:
                cursor.execute('''
                    INSERT INTO sync_errors (
                        sync_id, error_type, error_message, error_time, record_id, stack_trace
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s
                    )
                ''', (
                    sync_record.id, error['error_type'], error['error_message'],
                    error['time'], error['record_id'], error['stack_trace']
                ))
            
            # 保存变更记录（如果详细级别为detailed）
            if self.detail_level == 'detailed' and sync_record.changes:
                for change in sync_record.changes:
                    cursor.execute('''
                        INSERT INTO sync_changes (
                            sync_id, record_id, operation_type, field, old_value, new_value, change_time
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s
                        )
                    ''', (
                        sync_record.id, change['record_id'], change['operation_type'], change['field'],
                        json.dumps(change['old_value']) if isinstance(change['old_value'], (dict, list)) else change['old_value'],
                        json.dumps(change['new_value']) if isinstance(change['new_value'], (dict, list)) else change['new_value'],
                        change['time']
                    ))
            
            # 保存性能指标（如果有）
            if hasattr(sync_record, 'performance_metrics') and sync_record.performance_metrics:
                for metric_name, metric_value in sync_record.performance_metrics.items():
                    cursor.execute('''
                        INSERT INTO sync_performance_metrics (
                            sync_id, metric_name, metric_value
                        ) VALUES (
                            %s, %s, %s
                        )
                    ''', (
                        sync_record.id, metric_name, metric_value
                    ))

            # 清理旧记录
            if self.retention_days > 0:
                cursor.execute('''
                    DELETE FROM sync_records
                    WHERE start_time < DATE_SUB(NOW(), INTERVAL %s DAY)
                ''', (self.retention_days,))
            
            # 限制每个模型的记录数
            if self.max_records > 0:
                cursor.execute('''
                    DELETE r FROM sync_records r
                    JOIN (
                        SELECT id FROM sync_records
                        WHERE model_id = %s
                        ORDER BY start_time DESC
                        LIMIT %s, 18446744073709551615
                    ) AS old_records ON r.id = old_records.id
                ''', (sync_record.model_id, self.max_records))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info(f"Saved sync record {sync_record.id} for model {sync_record.model_id} to database")
        except Error as e:
            logger.error(f"Error saving sync record to database: {str(e)}")
    
    def _save_to_file(self, sync_record: SyncRecord):
        """将同步记录保存到文件"""
        try:
            # 确保同步记录目录存在
            base_config = cmdb_config.get('base', {})
            cache_dir = Path(base_config.get('global', {}).get('cache_dir', './cache'))
            sync_records_dir = cache_dir / 'sync_records'
            os.makedirs(sync_records_dir, exist_ok=True)
            
            # 保存同步记录
            record_file = sync_records_dir / f"{sync_record.id}.json"
            with open(record_file, 'w', encoding='utf-8') as f:
                json.dump(sync_record.to_dict(), f, ensure_ascii=False, indent=2)
            
            # 更新同步记录索引
            index_file = sync_records_dir / 'index.json'
            index = []
            if os.path.exists(index_file):
                try:
                    with open(index_file, 'r', encoding='utf-8') as f:
                        index = json.load(f)
                except:
                    index = []
            
            # 添加新记录到索引
            index.append({
                'id': sync_record.id,
                'model_id': sync_record.model_id,
                'start_time': sync_record.start_time.isoformat(),
                'end_time': sync_record.end_time.isoformat() if sync_record.end_time else None,
                'status': sync_record.status,
                'total_records': sync_record.total_records,
                'success_count': sync_record.success_count,
                'failed_count': sync_record.failed_count
            })
            
            # 限制索引大小
            model_records = [r for r in index if r['model_id'] == sync_record.model_id]
            if len(model_records) > self.max_records:
                # 按时间排序
                model_records.sort(key=lambda x: x['start_time'])
                # 获取要删除的记录
                records_to_delete = model_records[:-self.max_records]
                # 从索引中删除
                index = [r for r in index if r['id'] not in [d['id'] for d in records_to_delete]]
                # 删除文件
                for record in records_to_delete:
                    record_path = sync_records_dir / f"{record['id']}.json"
                    if os.path.exists(record_path):
                        os.remove(record_path)
            
            # 保存索引
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(index, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Saved sync record {sync_record.id} for model {sync_record.model_id} to file")
        except Exception as e:
            logger.error(f"Error saving sync record to file: {str(e)}")
    
    def get_record(self, record_id: str) -> Optional[Dict[str, Any]]:
        """获取同步记录"""
        if not self.enabled:
            return None
        
        if self.storage_type == 'database':
            return self._get_from_database(record_id)
        else:
            return self._get_from_file(record_id)
    
    def _get_from_database(self, record_id: str) -> Optional[Dict[str, Any]]:
        """从数据库获取同步记录"""
        try:
            conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port
            )
            
            cursor = conn.cursor(dictionary=True)
            
            # 获取主记录
            cursor.execute('''
                SELECT * FROM sync_records WHERE id = %s
            ''', (record_id,))
            
            record = cursor.fetchone()
            
            if not record:
                cursor.close()
                conn.close()
                return None
            
            # 获取错误记录
            cursor.execute('''
                SELECT error_type, error_message, error_time, record_id, stack_trace
                FROM sync_errors WHERE sync_id = %s
            ''', (record_id,))
            
            errors = cursor.fetchall()
            
            # 获取变更记录（如果详细级别为detailed）
            changes = []
            if self.detail_level == 'detailed':
                cursor.execute('''
                    SELECT record_id, field, old_value, new_value, change_time
                    FROM sync_changes WHERE sync_id = %s
                ''', (record_id,))
                
                changes = cursor.fetchall()

            # 获取性能指标
            cursor.execute('''
                    SELECT metric_name, metric_value
                    FROM sync_performance_metrics WHERE sync_id = %s
                ''', (record_id,))

            metrics = cursor.fetchall()
            performance_metrics = {}
            for metric in metrics:
                performance_metrics[metric['metric_name']] = metric['metric_value']

            cursor.close()
            conn.close()

            # 构建完整记录
            result = {
                'id': record['id'],
                'model_id': record['model_id'],
                'sync_type': record['sync_type'],
                'sync_direction': record['sync_direction'],
                'start_time': record['start_time'].isoformat() if record['start_time'] else None,
                'end_time': record['end_time'].isoformat() if record['end_time'] else None,
                'status': record['status'],
                # 'source_id': record['source_id'],
                'statistics': {
                    'total_records': record['total_records'],
                    'success_count': record['success_count'],
                    'failed_count': record['failed_count'],
                    'skipped_count': record['skipped_count'],
                    'created_count': record['created_count'],
                    'updated_count': record['updated_count'],
                    'deleted_count': record['deleted_count'],
                    # 'cloud_to_internal_count': record.get('cloud_to_internal_count', 0),
                    # 'internal_to_cloud_count': record.get('internal_to_cloud_count', 0)
                },
                'source_statistics': json.loads(record.get('source_statistics', '{}')) if record.get(
                    'source_statistics') else {},
                'errors': errors,
                'performance': {
                    'total_duration': record['total_duration'],
                    'fetch_duration': record['fetch_duration'],
                    'transform_duration': record['transform_duration'],
                    'update_duration': record['update_duration'],
                    'api_call_count': record['api_call_count'],
                    'metrics': performance_metrics
                },
                'changes': changes
            }
            
            return result
        except Error as e:
            logger.error(f"Error getting sync record from database: {str(e)}")
            return None
    
    def _get_from_file(self, record_id: str) -> Optional[Dict[str, Any]]:
        """从文件获取同步记录"""
        try:
            # 确保同步记录目录存在
            base_config = cmdb_config.get('base', {})
            cache_dir = Path(base_config.get('global', {}).get('cache_dir', './cache'))
            sync_records_dir = cache_dir / 'sync_records'
            
            # 获取同步记录
            record_file = sync_records_dir / f"{record_id}.json"
            if not os.path.exists(record_file):
                return None
            
            with open(record_file, 'r', encoding='utf-8') as f:
                record = json.load(f)
            
            return record
        except Exception as e:
            logger.error(f"Error getting sync record from file: {str(e)}")
            return None
    
    def get_records_by_model(self, model_id: str, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
        """获取指定模型的同步记录"""
        if not self.enabled:
            return []
        
        if self.storage_type == 'database':
            return self._get_records_by_model_from_database(model_id, limit, offset)
        else:
            return self._get_records_by_model_from_file(model_id, limit, offset)
    
    def _get_records_by_model_from_database(self, model_id: str, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
        """从数据库获取指定模型的同步记录"""
        try:
            conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port
            )

            cursor = conn.cursor(dictionary=True)

            # 获取主记录
            cursor.execute('''
                SELECT * FROM sync_records 
                WHERE model_id = %s
                ORDER BY start_time DESC
                LIMIT %s OFFSET %s
            ''', (model_id, limit, offset))

            records = cursor.fetchall()

            # 获取每个记录的错误信息
            result = []
            for record in records:
                # 获取错误记录
                cursor.execute('''
                    SELECT error_type, error_message, error_time, record_id, stack_trace
                    FROM sync_errors WHERE sync_id = %s
                    LIMIT 100
                ''', (record['id'],))

                errors = cursor.fetchall()

                # 构建完整记录
                record_dict = {
                    'id': record['id'],
                    'model_id': record['model_id'],
                    'sync_type': record['sync_type'],
                    'sync_direction': record['sync_direction'],
                    'start_time': record['start_time'].isoformat() if record['start_time'] else None,
                    'end_time': record['end_time'].isoformat() if record['end_time'] else None,
                    'status': record['status'],
                    'source_id': record['source_id'],
                    'statistics': {
                        'total_records': record['total_records'],
                        'success_count': record['success_count'],
                        'failed_count': record['failed_count'],
                        'skipped_count': record['skipped_count'],
                        'created_count': record['created_count'],
                        'updated_count': record['updated_count'],
                        'deleted_count': record['deleted_count'],
                        'cloud_to_internal_count': record.get('cloud_to_internal_count', 0),
                        'internal_to_cloud_count': record.get('internal_to_cloud_count', 0)
                    },
                    'source_statistics': json.loads(record.get('source_statistics', '{}')) if record.get('source_statistics') else {},
                    'errors': errors,
                    'performance': {
                        'total_duration': record['total_duration'],
                        'fetch_duration': record['fetch_duration'],
                        'transform_duration': record['transform_duration'],
                        'update_duration': record['update_duration'],
                        'api_call_count': record['api_call_count']
                    }
                }

                # 只有在详细级别为detailed时才获取变更记录
                if self.detail_level == 'detailed':
                    cursor.execute('''
                        SELECT record_id, field, old_value, new_value, change_time
                        FROM sync_changes WHERE sync_id = %s
                        LIMIT 100
                    ''', (record['id'],))

                    changes = cursor.fetchall()
                    record_dict['changes'] = changes

                result.append(record_dict)

            cursor.close()
            conn.close()

            return result
        except Error as e:
            logger.error(f"Error getting sync records from database: {str(e)}")
            return []

    def _get_records_by_model_from_file(self, model_id: str, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
        """从文件获取指定模型的同步记录"""
        try:
            # 确保同步记录目录存在
            base_config = cmdb_config.get('base', {})
            cache_dir = Path(base_config.get('global', {}).get('cache_dir', './cache'))
            sync_records_dir = cache_dir / 'sync_records'

            # 获取索引文件
            index_file = sync_records_dir / 'index.json'
            if not os.path.exists(index_file):
                return []

            with open(index_file, 'r', encoding='utf-8') as f:
                index = json.load(f)

            # 过滤指定模型的记录
            model_records = [r for r in index if r['model_id'] == model_id]

            # 按时间排序（降序）
            model_records.sort(key=lambda x: x['start_time'], reverse=True)

            # 应用分页
            paged_records = model_records[offset:offset+limit]

            # 获取完整记录
            result = []
            for record_info in paged_records:
                record_id = record_info['id']
                record_file = sync_records_dir / f"{record_id}.json"

                if os.path.exists(record_file):
                    with open(record_file, 'r', encoding='utf-8') as f:
                        record = json.load(f)
                        result.append(record)

            return result
        except Exception as e:
            logger.error(f"Error getting sync records from file: {str(e)}")
            return []

    def get_records_count(self, model_id: str = None) -> int:
        """获取同步记录数量

        Args:
            model_id: 模型ID，如果提供则只计算该模型的记录数

        Returns:
            记录数量
        """
        if not self.enabled:
            return 0

        if self.storage_type == 'database':
            return self._get_records_count_from_database(model_id)
        else:
            return self._get_records_count_from_file(model_id)

    def _get_records_count_from_database(self, model_id: str = None) -> int:
        """从数据库获取同步记录数量"""
        try:
            conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port
            )

            cursor = conn.cursor()

            if model_id:
                # 获取指定模型的记录数
                cursor.execute('SELECT COUNT(*) FROM sync_records WHERE model_id = %s', (model_id,))
            else:
                # 获取所有记录数
                cursor.execute('SELECT COUNT(*) FROM sync_records')

            count = cursor.fetchone()[0]

            cursor.close()
            conn.close()

            return count
        except Error as e:
            logger.error(f"Error getting sync records count from database: {str(e)}")
            return 0

    def _get_records_count_from_file(self, model_id: str = None) -> int:
        """从文件获取同步记录数量"""
        try:
            # 确保同步记录目录存在
            base_config = cmdb_config.get('base', {})
            cache_dir = Path(base_config.get('global', {}).get('cache_dir', './cache'))
            sync_records_dir = cache_dir / 'sync_records'

            # 获取索引文件
            index_file = sync_records_dir / 'index.json'
            if not os.path.exists(index_file):
                return 0

            with open(index_file, 'r', encoding='utf-8') as f:
                index = json.load(f)

            if model_id:
                # 过滤指定模型的记录
                return len([r for r in index if r['model_id'] == model_id])
            else:
                # 所有记录数
                return len(index)
        except Exception as e:
            logger.error(f"Error getting sync records count from file: {str(e)}")
            return 0

    def get_latest_record(self, model_id: str) -> Optional[Dict[str, Any]]:
        """获取指定模型的最新同步记录

        Args:
            model_id: 模型ID

        Returns:
            最新的同步记录，如果没有则返回None
        """
        if not self.enabled:
            return None

        # 获取最新的一条记录
        records = self.get_records_by_model(model_id, limit=1, offset=0)

        if records:
            return records[0]
        else:
            return None

    def delete_record(self, record_id: str) -> bool:
        """删除同步记录

        Args:
            record_id: 记录ID

        Returns:
            是否删除成功
        """
        if not self.enabled:
            return False

        if self.storage_type == 'database':
            return self._delete_from_database(record_id)
        else:
            return self._delete_from_file(record_id)

    def _delete_from_database(self, record_id: str) -> bool:
        """从数据库删除同步记录"""
        try:
            conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port
            )

            cursor = conn.cursor()

            # 删除记录（级联删除会自动删除关联的错误和变更记录）
            cursor.execute('DELETE FROM sync_records WHERE id = %s', (record_id,))

            conn.commit()
            cursor.close()
            conn.close()

            logger.info(f"Deleted sync record {record_id} from database")
            return True
        except Error as e:
            logger.error(f"Error deleting sync record from database: {str(e)}")
            return False

    def _delete_from_file(self, record_id: str) -> bool:
        """从文件删除同步记录"""
        try:
            # 确保同步记录目录存在
            base_config = cmdb_config.get('base', {})
            cache_dir = Path(base_config.get('global', {}).get('cache_dir', './cache'))
            sync_records_dir = cache_dir / 'sync_records'

            # 删除记录文件
            record_file = sync_records_dir / f"{record_id}.json"
            if os.path.exists(record_file):
                os.remove(record_file)

            # 更新索引
            index_file = sync_records_dir / 'index.json'
            if os.path.exists(index_file):
                with open(index_file, 'r', encoding='utf-8') as f:
                    index = json.load(f)

                # 从索引中删除
                index = [r for r in index if r['id'] != record_id]

                # 保存索引
                with open(index_file, 'w', encoding='utf-8') as f:
                    json.dump(index, f, ensure_ascii=False, indent=2)

            logger.info(f"Deleted sync record {record_id} from file")
            return True
        except Exception as e:
            logger.error(f"Error deleting sync record from file: {str(e)}")
            return False


class DatabaseMigrator:
    def __init__(self, host, user, password, database, port=3306):
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.port = port

    def _get_connection(self):
        return mysql.connector.connect(
            host=self.host,
            user=self.user,
            password=self.password,
            database=self.database,
            port=self.port
        )

    def init_migration_table(self):
        """创建迁移记录表"""
        conn = self._get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS db_migrations (
                id VARCHAR(100) PRIMARY KEY,
                applied_at DATETIME NOT NULL
            )
        ''')
        conn.commit()
        cursor.close()
        conn.close()

    def has_migration(self, migration_id):
        """检查迁移是否已经执行过"""
        conn = self._get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM db_migrations WHERE id = %s", (migration_id,))
        exists = cursor.fetchone()[0] > 0
        cursor.close()
        conn.close()
        return exists

    def mark_migration(self, migration_id):
        """记录迁移已执行"""
        conn = self._get_connection()
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO db_migrations (id, applied_at) VALUES (%s, %s)",
            (migration_id, datetime.now())
        )
        conn.commit()
        cursor.close()
        conn.close()

    def run_migrations(self):
        """执行所有未执行的迁移"""
        self.init_migration_table()

        # 手动维护 每次表变化 手动注册
        migrations = [
            ("20250825_add_operation_type_column", self._migration_add_operation_type),
            # ("20250825_update_operation_type_data", self._migration_update_operation_type_data),
        ]

        for migration_id, migration_func in migrations:
            if not self.has_migration(migration_id):
                logger.info(f"Running migration: {migration_id}")
                try:
                    migration_func()
                    self.mark_migration(migration_id)
                    logger.info(f"Migration applied: {migration_id}")
                except Error as e:
                    logger.error(f"Migration failed [{migration_id}]: {str(e)}")
                    raise

    # --- 迁移步骤定义 ---

    def _migration_add_operation_type(self):
        """迁移1：为 sync_changes 表新增 operation_type 字段"""
        conn = self._get_connection()
        cursor = conn.cursor()
        # 检查字段是否存在
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = %s
              AND table_name = 'sync_changes'
              AND column_name = 'operation_type'
        """, (self.database,))
        exists = cursor.fetchone()[0]
        if exists == 0:
            cursor.execute('''
                ALTER TABLE sync_changes
                ADD COLUMN operation_type VARCHAR(50) COMMENT '操作类型：created/updated/deleted' AFTER record_id
            ''')
            conn.commit()
        cursor.close()
        conn.close()

    def _migration_update_operation_type_data(self):
        """迁移2：为历史数据填充 operation_type"""
        conn = self._get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE sync_changes
            SET operation_type = new_value
            WHERE operation_type IS NULL
        """)
        affected = cursor.rowcount
        conn.commit()
        cursor.close()
        conn.close()
        logger.info(f"Updated {affected} rows in sync_changes")