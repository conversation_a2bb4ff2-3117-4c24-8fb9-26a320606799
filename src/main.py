import os
from src.logger_config import logger
import argparse
from src.config_loader import ConfigLoader
from src.sync_manager import SyncManager


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='CMDB模型同步工具')
    parser.add_argument('--config', default='./config/base_config.yaml', help='基础配置文件路径')
    parser.add_argument('--model', help='指定要同步的模型ID，不指定则同步所有启用的模型')
    parser.add_argument('--source', help='指定要使用的云数据源ID，不指定则使用所有配置的数据源')
    parser.add_argument('--force', action='store_true', help='强制刷新数据，不使用缓存')
    args = parser.parse_args()
    
    # 加载配置
    config_loader = ConfigLoader(args.config)
    config = config_loader.load_config()
    
    # 设置日志
    setup_logging()  # Setup logging configuration first
    logger.info("Starting CMDB model sync tool")
    
    # 检查指定的数据源是否存在
    if args.source:
        cloud_sources = config.get('base', {}).get('apis', {}).get('cloud_side', {}).keys()
        if not cloud_sources or args.source not in cloud_sources:
            logger.error(f"Specified source '{args.source}' not found in configuration")
            return
        logger.info(f"Using specified cloud data source: {args.source}")

    # 创建同步管理器
    sync_manager = SyncManager()
    
    # 初始化同步过程
    if args.model:
        # 同步指定模型
        models = config.get('models', {})
        if args.model in models:
            model_config = models[args.model]
            if model_config.get('enabled', True):
                logger.info(f"Initializing sync for model: {args.model}")

                # 修复：创建同步记录以便统计API调用
                from src.record_config import SyncRecord
                sync_direction = 'cloud_to_internal'  # 默认方向，可根据配置调整
                if 'sync_direction' in model_config:
                    sync_direction = model_config['sync_direction']
                elif model_config.get('internal_to_cloud', {}).get('enabled', False):
                    sync_direction = 'internal_to_cloud'
                elif model_config.get('bidirectional', {}).get('enabled', False):
                    sync_direction = 'bidirectional'

                sync_record = SyncRecord(args.model, 'full', sync_direction)
                sync_record.source_id = model_config.get('cloud_side', {}).get('source_id')
                sync_manager._current_sync_record = sync_record

                # 先全量获取云内侧和行内侧数据
                sync_manager.fetch_all_cloud_data(force_refresh=args.force, model_id=args.model, source_id=args.source)
                sync_manager.fetch_all_internal_data(force_refresh=args.force, model_id=args.model)
                # 然后同步指定模型（传入已创建的sync_record）
                sync_record = sync_manager.sync_model(args.model, model_config, sync_record)
                logger.info(f"Completed sync for model: {args.model}")
            else:
                logger.warning(f"Model {args.model} is disabled")
        else:
            logger.error(f"Model {args.model} not found in configuration")
    else:
        # 同步所有启用的模型
        logger.info("Initializing sync for all enabled models")
        # 修复：使用initialize_sync方法，它已经包含了API调用统计的修复
        sync_manager.initialize_sync(force_refresh=args.force)
        logger.info("Completed sync for all enabled models")
    
    logger.info("CMDB model sync tool completed")

if __name__ == "__main__":
    main()
